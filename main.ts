import 'dotenv/config';
import { MonikaBot } from './src/bot.js';

async function main() {
    console.log('🌸 Initializing Monika Bot...');
    console.log('📅 Started at:', new Date().toISOString());

    const bot = new MonikaBot();

    // Handle graceful shutdown
    process.on('SIGINT', async () => {
        console.log('\n🛑 Received SIGINT, shutting down gracefully...');
        try {
            await bot.stop();
            process.exit(0);
        } catch (error) {
            console.error('❌ Error during shutdown:', error);
            process.exit(1);
        }
    });

    process.on('SIGTERM', async () => {
        console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
        try {
            await bot.stop();
            process.exit(0);
        } catch (error) {
            console.error('❌ Error during shutdown:', error);
            process.exit(1);
        }
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
        console.error('❌ Uncaught Exception:', error);
        process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
        console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
        process.exit(1);
    });

    try {
        await bot.start();
        console.log('✅ Monika Bot is now running!');
        console.log('💚 Ready to chat with you~');

        // Optional: Log some stats periodically
        setInterval(() => {
            if (bot.isOnline()) {
                const conversations = bot.getAllConversations();
                console.log(`📊 Status: Online | Active conversations: ${conversations.length}`);
            }
        }, 300000); // Every 5 minutes

    } catch (error) {
        console.error('❌ Failed to start Monika Bot:', error);
        process.exit(1);
    }
}

// Start the bot
main().catch((error) => {
    console.error('❌ Fatal error in main:', error);
    process.exit(1);
});