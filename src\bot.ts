import * as Discord from 'discord-user-bots';
import { MessageHandler } from './utils/messageHandler.js';
import { AIService } from './utils/aiService.js';
import { StatusManager, DiscordStatus } from './utils/statusManager.js';
import { botConfig } from './config/personality.js';
import { DiscordMessage } from './types/message.js';

export class MonikaBot {
  private client: any;
  private messageHandler: MessageHandler;
  private aiService: AIService;
  private statusManager: StatusManager;
  private isReady: boolean = false;

  constructor() {
    this.client = new Discord.Client();
    this.messageHandler = new MessageHandler();
    this.aiService = new AIService();
    this.statusManager = new StatusManager(this.client, botConfig.status);
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.client.on('ready', async () => {
      await this.onReady();
    });

    this.client.on('message', async (message: any) => {
      await this.onMessage(message);
    });

    this.client.on('error', (error: Error) => {
      this.onError(error);
    });

    this.client.on('disconnect', () => {
      this.onDisconnect();
    });
  }

  private async onReady(): Promise<void> {
    console.log('🌟 Monika is online and ready!');
    console.log(`📝 Personality: ${botConfig.personality.traits.join(', ')}`);
    console.log(`🎯 Target User ID: ${botConfig.targetUserId}`);
    console.log(`💬 Max Context Messages: ${botConfig.maxContextMessages}`);
    console.log(`📚 Max Stored Messages: ${botConfig.maxStoredMessages}`);

    this.isReady = true;

    // Initialize status manager
    await this.statusManager.initialize();

    // Test AI service connection
    this.testAIConnection();
  }

  private async testAIConnection(): Promise<void> {
    try {
      const isConnected = await this.aiService.testConnection();
      if (isConnected) {
        console.log('✅ AI service connection successful');
      } else {
        console.log('❌ AI service connection failed');
      }
    } catch (error) {
      console.error('❌ Error testing AI connection:', error);
    }
  }

  private async onMessage(message: any): Promise<void> {
    try {
      // Skip if bot is not ready
      if (!this.isReady) {
        return;
      }

      // Convert to our message type
      const discordMessage: DiscordMessage = {
        id: message.id || Date.now().toString(),
        content: message.content || '',
        author: {
          id: message.author?.id || 'unknown',
          username: message.author?.username || 'Unknown User',
          discriminator: message.author?.discriminator,
          global_name: message.author?.global_name
        },
        channel_id: message.channel_id || message.channelId || '',
        timestamp: message.timestamp || new Date().toISOString(),
        edited_timestamp: message.edited_timestamp,
        attachments: message.attachments,
        embeds: message.embeds,
        mentions: message.mentions,
        mention_roles: message.mention_roles,
        pinned: message.pinned,
        mention_everyone: message.mention_everyone,
        tts: message.tts,
        type: message.type
      };

      if (discordMessage.author.id === botConfig.botId) {
        // Ignore messages from the bot itself
        return;
      }

      // if (discordMessage.author.id !== botConfig.targetUserId) {
      //   // Ignore messages not from the target user
      //   console.log(`🔕 Ignored message from ${discordMessage.author.username || discordMessage.author.global_name}`);
      //   return;
      // }

      // Log the message for debugging
      console.log(`📨 Message received:`, {
        author: discordMessage.author.username || discordMessage.author.global_name,
        content: discordMessage.content.substring(0, 100) + (discordMessage.content.length > 100 ? '...' : ''),
        channel: discordMessage.channel_id
      });

      // Handle the message
      await this.messageHandler.handleMessage(this.client, discordMessage);

      // Update status manager with message activity
      await this.statusManager.onMessageActivity();

    } catch (error) {
      console.error('❌ Error processing message:', error);
    }
  }

  private onError(error: Error): void {
    console.error('❌ Discord client error:', error);
  }

  private onDisconnect(): void {
    console.log('🔌 Monika disconnected from Discord');
    this.isReady = false;
  }

  async start(): Promise<void> {
    try {
      // Check if required environment variables are set
      if (!process.env.USER_TOKEN) {
        throw new Error('USER_TOKEN environment variable is required');
      }

      if (!process.env.DEEPSEEK_API_KEY) {
        throw new Error('DEEPSEEK_API_KEY environment variable is required');
      }

      console.log('🚀 Starting Monika bot...');
      console.log('🔑 Logging in to Discord...');
      
      await this.client.login(process.env.USER_TOKEN);
      
    } catch (error) {
      console.error('❌ Failed to start bot:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    try {
      console.log('🛑 Stopping Monika bot...');
      this.isReady = false;

      // Stop status manager
      this.statusManager.stop();

      if (this.client) {
        await this.client.destroy();
      }

      console.log('👋 Monika has been stopped');
    } catch (error) {
      console.error('❌ Error stopping bot:', error);
      throw error;
    }
  }

  // Utility methods for external access
  getConversationStats(channelId: string) {
    return this.messageHandler.getConversationStats(channelId);
  }

  async exportConversation(channelId: string): Promise<string | null> {
    return this.messageHandler.exportConversation(channelId);
  }

  async clearConversation(channelId: string): Promise<boolean> {
    return this.messageHandler.clearConversation(channelId);
  }

  getAllConversations(): string[] {
    return this.messageHandler.getAllConversations();
  }

  isOnline(): boolean {
    return this.isReady;
  }

  // Method to send a message programmatically
  async sendMessage(channelId: string, content: string): Promise<void> {
    try {
      if (!this.isReady) {
        throw new Error('Bot is not ready');
      }

      await this.client.send(channelId, { content });
      console.log(`📤 Sent message to ${channelId}: ${content.substring(0, 50)}...`);
    } catch (error) {
      console.error('❌ Error sending message:', error);
      throw error;
    }
  }

  // Method to update bot configuration at runtime
  updateTargetUser(userId: string): void {
    (botConfig as any).targetUserId = userId;
    console.log(`🎯 Updated target user ID to: ${userId}`);
  }

  // Status management methods
  async setStatus(status: DiscordStatus): Promise<void> {
    await this.statusManager.forceSetStatus(status);
  }

  getCurrentStatus(): DiscordStatus {
    return this.statusManager.getCurrentStatus();
  }

  getStatusStats(): any {
    return this.statusManager.getStats();
  }

  updateStatusConfig(config: any): void {
    this.statusManager.updateConfig(config);
  }

  // Memory management methods
  async getMemoryStats(userId: string): Promise<any> {
    return await this.messageHandler.getMemoryStats(userId);
  }

  async addManualMemory(userId: string, memoryText: string, category?: string): Promise<boolean> {
    return await this.messageHandler.addManualMemory(userId, memoryText, category);
  }

  async clearUserMemories(userId: string): Promise<boolean> {
    return await this.messageHandler.clearUserMemories(userId);
  }

  async getMemoryContext(userId: string, currentMessage?: string): Promise<any> {
    return await this.messageHandler.getMemoryContext(userId, currentMessage);
  }
}
