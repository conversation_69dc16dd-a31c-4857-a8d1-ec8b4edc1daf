import { UserTool, ToolResponse } from "../types/tools";
import { AIService } from "../utils/aiService";
import { MemoryStorage } from "../utils/memoryStorage";

export class Tool {
    name: string;
    description: string;
    parameters: string[];

    aiServce: AIService;
    storage: MemoryStorage;

    constructor(name: string, description: string, parameters: string[]) {
        this.name = name;
        this.description = description;
        this.parameters = parameters;
    }

    toJSON(): UserTool {
        return {
            name: this.name,
            description: this.description,
            parameters: this.parameters
        };
    }

    async isToolNeeded(userMessage: string): Promise<boolean> {
        // Placeholder for tool detection logic
        // This should be overridden by subclasses or specific tool implementations
        return false;
    }

    async execute(parameters: Record<string, any>): Promise<ToolResponse> {
        // Placeholder for tool execution logic
        // This should be overridden by subclasses or specific tool implementations
        console.warn(`Tool ${this.name} does not implement execute method.`);
        return {
            tool: this.name,
            parameters: []
        };
    }
}