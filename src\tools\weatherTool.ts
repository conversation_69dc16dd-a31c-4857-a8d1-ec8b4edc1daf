import { DiscordMessage } from "../types/message";
import { Tool } from "./tool";

export class WeatherTool extends Tool {
  constructor() {
    super(
      "weather",
      "Get current weather information for a specified location.",
      ["location"]
    );
  }

  async execute(message: DiscordMessage): Promise<any> {
    const systemPrompt = `You are a weather information AI. Obtain where does the user want to know the weather for? The user message is: "${message.content}". Here are the memories I have about the user: ${this.storage.getUserMemories(message.author.id) || "No memory available."}. If the user has previously mentioned a location, use that. Return the name of the location ONLY to use that location in OpenWeather API.`;

    const response = await this.aiServce.customAiRequest(systemPrompt, message.content);
    if (!response) {
        throw new Error("No response generated from AI service for weather tool.");
        }
    // Ensure response isn't too long for Discord
    if (response.length > 2000) {
        return response.substring(0, 1997) + '...';
    }

    return {
      tool: this.name,
      parameters: message.content
    };
  }

  async isToolNeeded(userMessage: string): Promise<boolean> {
    try {
        const systemPrompt = `You are a tool-detecting AI. Determine if the user's message requires the weather tool to provide a proper response. The user message is: "${userMessage}". If the weather tool is needed, return true; otherwise, return false.`;
        const response = await this.aiServce.customAiRequest(systemPrompt, userMessage);
        const lowerResponse = response.toLowerCase().trim();
        return lowerResponse === 'true';
    } catch (error) {
        console.error('Error checking if weather tool is needed:', error);
        return false;
    }
  }
}