export interface DiscordMessage {
  id: string;
  content: string;
  author: {
    id: string;
    username: string;
    discriminator?: string;
    global_name?: string;
  };
  channel_id: string;
  timestamp: string;
  edited_timestamp?: string;
  attachments?: any[];
  embeds?: any[];
  mentions?: any[];
  mention_roles?: string[];
  pinned?: boolean;
  mention_everyone?: boolean;
  tts?: boolean;
  type?: number;
}

export interface ConversationContext {
  channelId: string;
  messages: DiscordMessage[];
  lastUpdated: string;
  messageCount: number;
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  timestamp?: string;
  author?: string;
}

// Memory system types
export interface UserMemory {
  memory: string;
  timestamp: string;
  messageId?: string;
  channelId?: string;
  confidence?: number; // AI confidence in memory importance (0-1)
  category?: string; // e.g., 'preference', 'personal', 'event', 'relationship'
}

export interface MemoryEvaluation {
  isMemoryWorthy: boolean;
  extractedMemory?: string;
  confidence: number;
  category?: string;
  reasoning?: string;
}

export interface UserMemoryStorage {
  userId: string;
  memories: UserMemory[];
  lastUpdated: string;
  totalMemories: number;
}

export interface MemoryContext {
  recentMemories: UserMemory[];
  relevantMemories: UserMemory[];
  memoryCount: number;
}
