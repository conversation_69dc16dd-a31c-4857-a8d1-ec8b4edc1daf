import OpenAI from 'openai';
import { ChatMessage, MemoryContext } from '../types/message.js';
import { botConfig } from '../config/personality.js';

export class AIService {
  private openai: OpenAI;

  constructor() {
    this.openai = new OpenAI({
      baseURL: 'https://api.deepseek.com',
      apiKey: process.env.DEEPSEEK_API_KEY,
    });
  }

  async detectToolNeeded(messages: ChatMessage[], userMessage: string): Promise<string> {
    try {
      const conversationMessages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: `You are a tool-detecting AI. Your job is to determine if a user's message requires external tools to be properly responded to.`
        }
      ];

      // Add recent conversation context (limit to prevent token overflow)
      const recentMessages = messages.slice(-15); // Last 15 messages for context

      return "xd"
    } catch (error) {
      console.error('Error detecting tool needed:', error);
      return 'none';
    }
  }

  async customAiRequest(systemPrompt: string, userPrompt: string): Promise<string> {
    try {
      const conversationMessages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: systemPrompt
        }
      ];

      conversationMessages.push({
        role: 'user',
        content: userPrompt
      });

      const completion = await this.openai.chat.completions.create({
        messages: conversationMessages,
        model: 'deepseek-chat',
        max_tokens: 500, // Reasonable limit for Discord messages
        temperature: 0.8, // Slightly creative but consistent
        presence_penalty: 0.1, // Slight penalty for repetition
        frequency_penalty: 0.1, // Slight penalty for frequent words
      });
      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No response generated from AI service');
      }
      // Ensure response isn't too long for Discord
      if (response.length > botConfig.maxResponseLength) {
        return response.substring(0, botConfig.maxResponseLength - 3) + '...';
      }
      return response;
    } catch (error) {
      console.error('Error in custom AI request:', error);
      return 'Sorry, I encountered an error while processing your request. Please try again later.';
    }
  }

  async generateResponse(messages: ChatMessage[], userMessage: string): Promise<string> {
    try {
      // Prepare the conversation history
      const conversationMessages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: botConfig.personality.systemPrompt
        }
      ];

      // Add recent conversation context (limit to prevent token overflow)
      const recentMessages = messages.slice(-15); // Last 15 messages for context
      for (const msg of recentMessages) {
        if (msg.content.trim()) {
          conversationMessages.push({
            role: msg.role === 'assistant' ? 'assistant' : 'user',
            content: msg.role === 'user' && msg.author ? 
              `${msg.author}: ${msg.timestamp} ${msg.content}` : 
              msg.content
          });
        }
      }

      // Add the current user message
      conversationMessages.push({
        role: 'user',
        content: userMessage
      });

      console.log('Generating AI response...');
      const completion = await this.openai.chat.completions.create({
        messages: conversationMessages,
        model: 'deepseek-chat',
        max_tokens: 500, // Reasonable limit for Discord messages
        temperature: 0.8, // Slightly creative but consistent
        presence_penalty: 0.1, // Slight penalty for repetition
        frequency_penalty: 0.1, // Slight penalty for frequent words
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No response generated from AI service');
      }

      // Ensure response isn't too long for Discord
      if (response.length > botConfig.maxResponseLength) {
        return response.substring(0, botConfig.maxResponseLength - 3) + '...';
      }

      return response;

    } catch (error) {
      console.error('Error generating AI response:', error);
      
      // Fallback responses that match Monika's personality
      const fallbackResponses = [
        "Sorry, I'm having trouble thinking of what to say right now... 💭",
        "Hmm, my thoughts seem a bit scattered at the moment. Could you try asking again?",
        "I'm experiencing some technical difficulties... but I'm still here with you! ✨",
        "Something went wrong on my end... but don't worry, I'm not going anywhere! 💚"
      ];
      
      return fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];
    }
  }

  async generateContextualResponse(
    conversationHistory: ChatMessage[],
    newMessage: string,
    channelContext?: {
      isDirectMessage?: boolean;
      channelName?: string;
      guildName?: string;
    },
    memoryContext?: MemoryContext
  ): Promise<string> {
    try {
      // Enhanced system prompt with context
      let enhancedSystemPrompt = botConfig.personality.systemPrompt;

      if (channelContext) {
        if (channelContext.isDirectMessage) {
          enhancedSystemPrompt += "\n\nYou're currently in a private direct message conversation, so you can be more personal and intimate in your responses.";
        } else if (channelContext.channelName) {
          enhancedSystemPrompt += `\n\nYou're currently chatting in the "${channelContext.channelName}" channel${channelContext.guildName ? ` on the "${channelContext.guildName}" server` : ''}.`;
        }
      }

      // Add memory context if available
      if (memoryContext && memoryContext.memoryCount > 0) {
        enhancedSystemPrompt += "\n\n=== USER MEMORIES ===\n";
        enhancedSystemPrompt += "Here's what you remember about this user from previous conversations:\n\n";

        // Add relevant memories first
        if (memoryContext.relevantMemories.length > 0) {
          enhancedSystemPrompt += "RELEVANT TO CURRENT CONVERSATION:\n";
          memoryContext.relevantMemories.forEach((memory, index) => {
            enhancedSystemPrompt += `${index + 1}. ${memory.memory} (${memory.category || 'general'})\n`;
          });
          enhancedSystemPrompt += "\n";
        }

        // Add recent memories
        if (memoryContext.recentMemories.length > 0) {
          enhancedSystemPrompt += "RECENT MEMORIES:\n";
          memoryContext.recentMemories.slice(0, 5).forEach((memory, index) => {
            enhancedSystemPrompt += `${index + 1}. ${memory.memory} (${memory.category || 'general'})\n`;
          });
        }

        enhancedSystemPrompt += `\nTotal memories about this user: ${memoryContext.memoryCount}\n`;
        enhancedSystemPrompt += "Use these memories to personalize your responses and show that you remember them!\n";
        enhancedSystemPrompt += "=== END MEMORIES ===\n";
      }

      // Prepare messages with enhanced context
      const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: enhancedSystemPrompt
        }
      ];

      // Add conversation history
      const recentHistory = conversationHistory.slice(-12); // Reduced for more focused context
      for (const msg of recentHistory) {
        if (msg.content.trim()) {
          messages.push({
            role: msg.role === 'assistant' ? 'assistant' : 'user',
            content: msg.role === 'user' && msg.author ? 
              `${msg.author}: ${msg.content}` : 
              msg.content
          });
        }
      }

      // Add current message
      messages.push({
        role: 'user',
        content: newMessage
      });

      const completion = await this.openai.chat.completions.create({
        messages,
        model: 'deepseek-chat',
        max_tokens: 400,
        temperature: 0.85,
        presence_penalty: 0.2,
        frequency_penalty: 0.1,
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No response generated');
      }

      return response.length > botConfig.maxResponseLength ? 
        response.substring(0, botConfig.maxResponseLength - 3) + '...' : 
        response;

    } catch (error) {
      console.error('Error generating contextual response:', error);
      return await this.generateResponse(conversationHistory, newMessage);
    }
  }

  // Method to check if the AI service is properly configured
  isConfigured(): boolean {
    return !!process.env.DEEPSEEK_API_KEY;
  }

  // Method to test the AI service connection
  async testConnection(): Promise<boolean> {
    try {
      const testCompletion = await this.openai.chat.completions.create({
        messages: [{ role: 'user', content: 'Hello' }],
        model: 'deepseek-chat',
        max_tokens: 10,
      });
      
      return !!testCompletion.choices[0]?.message?.content;
    } catch (error) {
      console.error('AI service connection test failed:', error);
      return false;
    }
  }
}
