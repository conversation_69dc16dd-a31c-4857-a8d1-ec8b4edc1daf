import * as fs from 'fs';
import * as path from 'path';
import { ConversationContext, DiscordMessage } from '../types/message.js';

export interface ConversationMetadata {
  channelId: string;
  channelName?: string;
  guildId?: string;
  guildName?: string;
  participantCount: number;
  firstMessageDate: string;
  lastMessageDate: string;
  totalMessages: number;
  botResponseCount: number;
}

export class ConversationStorage {
  private dataDir: string;
  private metadataFile: string;

  constructor(dataDirectory: string = './data/conversations') {
    this.dataDir = dataDirectory;
    this.metadataFile = path.join(dataDirectory, '_metadata.json');
    this.ensureDataDirectory();
  }

  private ensureDataDirectory(): void {
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
    }
  }

  async saveConversation(context: ConversationContext, additionalInfo?: {
    channelName?: string;
    guildId?: string;
    guildName?: string;
  }): Promise<void> {
    try {
      const filePath = path.join(this.dataDir, `${context.channelId}.json`);
      
      // Create enhanced conversation data
      const conversationData = {
        ...context,
        metadata: {
          channelName: additionalInfo?.channelName,
          guildId: additionalInfo?.guildId,
          guildName: additionalInfo?.guildName,
          savedAt: new Date().toISOString(),
          version: '1.0'
        }
      };

      // Save conversation file
      fs.writeFileSync(filePath, JSON.stringify(conversationData, null, 2), 'utf8');
      
      // Update metadata
      await this.updateMetadata(context, additionalInfo);
      
      console.log(`Saved conversation for channel ${context.channelId}`);
    } catch (error) {
      console.error(`Error saving conversation for channel ${context.channelId}:`, error);
    }
  }

  private async updateMetadata(context: ConversationContext, additionalInfo?: {
    channelName?: string;
    guildId?: string;
    guildName?: string;
  }): Promise<void> {
    try {
      let metadata: { [channelId: string]: ConversationMetadata } = {};
      
      // Load existing metadata
      if (fs.existsSync(this.metadataFile)) {
        const data = fs.readFileSync(this.metadataFile, 'utf8');
        metadata = JSON.parse(data);
      }

      // Calculate statistics
      const messages = context.messages;
      const botResponseCount = messages.filter(msg => 
        msg.author.username?.toLowerCase().includes('monika') || 
        msg.author.global_name?.toLowerCase().includes('monika')
      ).length;

      const participants = new Set(messages.map(msg => msg.author.id));
      const timestamps = messages.map(msg => new Date(msg.timestamp).getTime()).filter(t => !isNaN(t));
      
      const firstMessageDate = timestamps.length > 0 ? 
        new Date(Math.min(...timestamps)).toISOString() : 
        new Date().toISOString();
      
      const lastMessageDate = timestamps.length > 0 ? 
        new Date(Math.max(...timestamps)).toISOString() : 
        new Date().toISOString();

      // Update metadata for this channel
      metadata[context.channelId] = {
        channelId: context.channelId,
        channelName: additionalInfo?.channelName,
        guildId: additionalInfo?.guildId,
        guildName: additionalInfo?.guildName,
        participantCount: participants.size,
        firstMessageDate,
        lastMessageDate,
        totalMessages: messages.length,
        botResponseCount
      };

      // Save updated metadata
      fs.writeFileSync(this.metadataFile, JSON.stringify(metadata, null, 2), 'utf8');
    } catch (error) {
      console.error('Error updating metadata:', error);
    }
  }

  loadConversation(channelId: string): ConversationContext | null {
    try {
      const filePath = path.join(this.dataDir, `${channelId}.json`);
      
      if (!fs.existsSync(filePath)) {
        return null;
      }

      const data = fs.readFileSync(filePath, 'utf8');
      const conversationData = JSON.parse(data);
      
      // Return just the conversation context part
      return {
        channelId: conversationData.channelId,
        messages: conversationData.messages,
        lastUpdated: conversationData.lastUpdated,
        messageCount: conversationData.messageCount
      };
    } catch (error) {
      console.error(`Error loading conversation for channel ${channelId}:`, error);
      return null;
    }
  }

  getAllConversationMetadata(): { [channelId: string]: ConversationMetadata } {
    try {
      if (!fs.existsSync(this.metadataFile)) {
        return {};
      }

      const data = fs.readFileSync(this.metadataFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error('Error loading conversation metadata:', error);
      return {};
    }
  }

  getConversationList(): string[] {
    try {
      const files = fs.readdirSync(this.dataDir);
      return files
        .filter(file => file.endsWith('.json') && file !== '_metadata.json')
        .map(file => file.replace('.json', ''));
    } catch (error) {
      console.error('Error getting conversation list:', error);
      return [];
    }
  }

  deleteConversation(channelId: string): boolean {
    try {
      const filePath = path.join(this.dataDir, `${channelId}.json`);
      
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        
        // Remove from metadata
        const metadata = this.getAllConversationMetadata();
        delete metadata[channelId];
        fs.writeFileSync(this.metadataFile, JSON.stringify(metadata, null, 2), 'utf8');
        
        console.log(`Deleted conversation for channel ${channelId}`);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error(`Error deleting conversation for channel ${channelId}:`, error);
      return false;
    }
  }

  exportConversationAsText(channelId: string): string | null {
    try {
      const context = this.loadConversation(channelId);
      if (!context) {
        return null;
      }

      let text = `Conversation Export - Channel: ${channelId}\n`;
      text += `Exported: ${new Date().toISOString()}\n`;
      text += `Total Messages: ${context.messages.length}\n`;
      text += '='.repeat(50) + '\n\n';

      for (const message of context.messages) {
        const timestamp = new Date(message.timestamp).toLocaleString();
        const author = message.author.global_name || message.author.username || message.author.id;
        text += `[${timestamp}] ${author}: ${message.content}\n\n`;
      }

      return text;
    } catch (error) {
      console.error(`Error exporting conversation for channel ${channelId}:`, error);
      return null;
    }
  }
}
