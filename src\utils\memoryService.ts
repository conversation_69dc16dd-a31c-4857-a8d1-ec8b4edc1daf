import OpenAI from 'openai';
import { DiscordMessage, UserMemory, MemoryEvaluation, MemoryContext } from '../types/message.js';
import { MemoryStorage } from './memoryStorage.ts';

export class MemoryService {
  private openai: OpenAI;
  private storage: MemoryStorage;

  constructor() {
    this.openai = new OpenAI({
      baseURL: 'https://api.deepseek.com',
      apiKey: process.env.DEEPSEEK_API_KEY,
    });
    this.storage = new MemoryStorage();
  }

  /**
   * Evaluates if a message contains memory-worthy information
   */
  async evaluateMessage(message: DiscordMessage): Promise<MemoryEvaluation> {
    let rawResponse = '';
    try {
      const evaluationPrompt = this.buildEvaluationPrompt(message.content, message.author.username || message.author.global_name || 'User');

      const completion = await this.openai.chat.completions.create({
        messages: [
          {
            role: 'system',
            content: `You are a memory evaluation AI. Your job is to determine if a message contains information worth remembering about a user for long-term personalization.

MEMORY-WORTHY CRITERIA:
- Personal preferences (likes, dislikes, hobbies, interests)
- Important personal information (birthday, location, job, relationships)
- Significant life events or experiences
- Goals, dreams, or aspirations
- Personality traits or characteristics
- Important memories or stories they share
- Recurring topics they care about

NOT MEMORY-WORTHY:
- Casual greetings or small talk
- Temporary states (current mood, what they're doing right now)
- Questions without personal context
- Generic responses or reactions
- Technical discussions without personal preference
- Jokes or memes without personal significance

Respond ONLY with a valid JSON object (no markdown, no explanations, no code blocks):
{
  "isMemoryWorthy": boolean,
  "extractedMemory": "concise memory if worthy (1 sentence max)",
  "confidence": number (0-1),
  "category": "preference|personal|event|relationship|trait|goal",
  "reasoning": "brief explanation"
}

Example response:
{"isMemoryWorthy": true, "extractedMemory": "User prefers tea over coffee", "confidence": 0.8, "category": "preference", "reasoning": "Clear preference statement"}`
          },
          {
            role: 'user',
            content: evaluationPrompt
          }
        ],
        model: 'deepseek-chat',
        max_tokens: 200,
        temperature: 0.3, // Lower temperature for more consistent evaluation
      });

      const response = completion.choices[0]?.message?.content;
      rawResponse = response || '';
      if (!response) {
        throw new Error('No evaluation response received');
      }

      // Clean the response to extract JSON (remove markdown formatting)
      let cleanedResponse = response.trim();

      // Remove markdown code blocks if present
      if (cleanedResponse.startsWith('```json')) {
        cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanedResponse.startsWith('```')) {
        cleanedResponse = cleanedResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      // Try to find JSON object in the response
      const jsonMatch = cleanedResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanedResponse = jsonMatch[0];
      }

      // Parse the JSON response
      console.log('🔍 Raw AI response:', response);
      console.log('🧹 Cleaned response:', cleanedResponse);

      const evaluation = JSON.parse(cleanedResponse) as MemoryEvaluation;

      // Validate the response
      if (typeof evaluation.isMemoryWorthy !== 'boolean' ||
          typeof evaluation.confidence !== 'number') {
        throw new Error('Invalid evaluation response format');
      }

      console.log('✅ Memory evaluation result:', evaluation);
      return evaluation;

    } catch (error) {
      console.error('❌ Error evaluating message for memory:', error);
      if (error instanceof SyntaxError) {
        console.error('📝 Failed to parse AI response as JSON. Raw response:', rawResponse);
      }

      // Return a safe default evaluation
      return {
        isMemoryWorthy: false,
        confidence: 0,
        reasoning: 'Evaluation failed'
      };
    }
  }

  /**
   * Stores a memory for a user if it passes evaluation
   */
  async processMessageForMemory(message: DiscordMessage): Promise<boolean> {
    try {
      const evaluation = await this.evaluateMessage(message);

      if (!evaluation.isMemoryWorthy || !evaluation.extractedMemory) {
        console.log(`📝 Message not memory-worthy: ${evaluation.reasoning}`);
        return false;
      }

      // Only store memories with reasonable confidence
      if (evaluation.confidence < 0.6) {
        console.log(`📝 Memory confidence too low (${evaluation.confidence}): ${evaluation.extractedMemory}`);
        return false;
      }

      const memory: UserMemory = {
        memory: evaluation.extractedMemory,
        timestamp: new Date().toISOString(),
        messageId: message.id,
        channelId: message.channel_id,
        confidence: evaluation.confidence,
        category: evaluation.category
      };

      const success = await this.storage.addMemory(message.author.id, memory);

      if (success) {
        console.log(`💾 Stored memory for ${message.author.username}: ${memory.memory}`);
      }

      return success;

    } catch (error) {
      console.error('Error processing message for memory:', error);
      return false;
    }
  }

  /**
   * Evaluates the last 5 messages with context to process memories and avoid duplication
   */
  async processMessagesWithContext(messages: DiscordMessage[], userId: string): Promise<{
    processedCount: number;
    newMemories: UserMemory[];
    skippedDuplicates: number;
  }> {
    try {
      // Filter messages from the target user and get last 5
      const userMessages = messages
        .filter(msg => msg.author.id === userId)
        .slice(-5);

      if (userMessages.length === 0) {
        return { processedCount: 0, newMemories: [], skippedDuplicates: 0 };
      }

      console.log(`🧠 Processing ${userMessages.length} messages with context for memory evaluation`);

      // Get existing memories to check for duplicates
      const existingMemories = await this.storage.getUserMemories(userId);

      // Evaluate all messages together with context
      const contextualEvaluation = await this.evaluateMessagesWithContext(userMessages, existingMemories);

      let processedCount = 0;
      let skippedDuplicates = 0;
      const newMemories: UserMemory[] = [];

      // Process each evaluated memory
      for (const evaluation of contextualEvaluation.memories) {
        if (!evaluation.isMemoryWorthy || !evaluation.extractedMemory) {
          continue;
        }

        // Check confidence threshold
        if (evaluation.confidence < 0.6) {
          console.log(`📝 Memory confidence too low (${evaluation.confidence}): ${evaluation.extractedMemory}`);
          continue;
        }

        // Check for duplicates against existing memories
        const isDuplicate = this.checkForDuplicateMemory(evaluation.extractedMemory, existingMemories);
        if (isDuplicate) {
          console.log(`🔄 Duplicate memory detected, skipping: ${evaluation.extractedMemory}`);
          skippedDuplicates++;
          continue;
        }

        // Create and store the memory
        const memory: UserMemory = {
          memory: evaluation.extractedMemory,
          timestamp: new Date().toISOString(),
          messageId: evaluation.sourceMessageId,
          channelId: userMessages[0].channel_id,
          confidence: evaluation.confidence,
          category: evaluation.category
        };

        const success = await this.storage.addMemory(userId, memory);

        if (success) {
          console.log(`💾 Stored contextual memory: ${memory.memory}`);
          newMemories.push(memory);
          processedCount++;
        }
      }

      return { processedCount, newMemories, skippedDuplicates };

    } catch (error) {
      console.error('Error processing messages with context:', error);
      return { processedCount: 0, newMemories: [], skippedDuplicates: 0 };
    }
  }

  /**
   * Retrieves relevant memories for a user to include in AI context
   */
  async getMemoryContext(userId: string, currentMessage?: string): Promise<MemoryContext> {
    try {
      const userMemories = await this.storage.getUserMemories(userId);
      
      if (!userMemories || userMemories.length === 0) {
        return {
          recentMemories: [],
          relevantMemories: [],
          memoryCount: 0
        };
      }

      // Get recent memories (last 10)
      const recentMemories = userMemories
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 10);

      // If we have a current message, try to find relevant memories
      let relevantMemories: UserMemory[] = [];
      if (currentMessage) {
        relevantMemories = await this.findRelevantMemories(userMemories, currentMessage);
      }

      return {
        recentMemories,
        relevantMemories,
        memoryCount: userMemories.length
      };

    } catch (error) {
      console.error('Error getting memory context:', error);
      return {
        recentMemories: [],
        relevantMemories: [],
        memoryCount: 0
      };
    }
  }

  /**
   * Finds memories relevant to the current message context
   */
  private async findRelevantMemories(memories: UserMemory[], currentMessage: string): Promise<UserMemory[]> {
    try {
      // Simple keyword matching for now - could be enhanced with semantic search
      const keywords = currentMessage.toLowerCase().split(' ').filter(word => word.length > 3);

      const relevantMemories = memories.filter(memory => {
        const memoryText = memory.memory.toLowerCase();
        return keywords.some(keyword => memoryText.includes(keyword));
      });

      // Return top 5 most relevant memories, prioritizing recent ones
      return relevantMemories
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 5);

    } catch (error) {
      console.error('Error finding relevant memories:', error);
      return [];
    }
  }

  /**
   * Evaluates multiple messages with context to extract memories while avoiding duplicates
   */
  private async evaluateMessagesWithContext(
    messages: DiscordMessage[],
    existingMemories: UserMemory[]
  ): Promise<{
    memories: Array<MemoryEvaluation & { sourceMessageId: string }>;
    contextSummary: string;
  }> {
    try {
      const username = messages[0]?.author.username || messages[0]?.author.global_name || 'User';

      // Build context from messages
      const messageContext = messages.map((msg, index) =>
        `Message ${index + 1} (${new Date(msg.timestamp).toLocaleString()}): "${msg.content}"`
      ).join('\n');

      // Build existing memories context
      const existingMemoriesContext = existingMemories.length > 0
        ? existingMemories.slice(-10).map(mem => `- ${mem.memory}`).join('\n')
        : 'No existing memories';

      const evaluationPrompt = `Analyze these recent messages from user "${username}" for memory-worthy information. Consider the conversation context and avoid duplicating existing memories.

RECENT MESSAGES:
${messageContext}

EXISTING MEMORIES (to avoid duplication):
${existingMemoriesContext}

MEMORY-WORTHY CRITERIA:
- Personal preferences (likes, dislikes, hobbies, interests)
- Important personal information (birthday, location, job, relationships)
- Significant life events or experiences
- Goals, dreams, or aspirations
- Personality traits or characteristics
- Important memories or stories they share
- Recurring topics they care about

NOT MEMORY-WORTHY:
- Casual greetings or small talk
- Temporary states (current mood, what they're doing right now)
- Questions without personal context
- Generic responses or reactions
- Information already captured in existing memories

Respond ONLY with a valid JSON object (no markdown, no explanations, no code blocks):
{
  "memories": [
    {
      "isMemoryWorthy": boolean,
      "extractedMemory": "concise memory if worthy (1 sentence max)",
      "confidence": number (0-1),
      "category": "preference|personal|event|relationship|trait|goal",
      "reasoning": "brief explanation",
      "sourceMessageId": "message_id_from_which_this_memory_was_extracted"
    }
  ],
  "contextSummary": "brief summary of the conversation context"
}`;

      const completion = await this.openai.chat.completions.create({
        messages: [
          {
            role: 'system',
            content: `You are a memory evaluation AI that analyzes conversations to extract meaningful information about users while avoiding duplicates. Focus on new, unique information not already captured in existing memories.`
          },
          {
            role: 'user',
            content: evaluationPrompt
          }
        ],
        model: 'deepseek-chat',
        max_tokens: 800,
        temperature: 0.3,
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No evaluation response received');
      }

      // Clean and parse the response
      let cleanedResponse = response.trim();
      if (cleanedResponse.startsWith('```json')) {
        cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanedResponse.startsWith('```')) {
        cleanedResponse = cleanedResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      const jsonMatch = cleanedResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanedResponse = jsonMatch[0];
      }

      console.log('🔍 Contextual evaluation response:', cleanedResponse);
      const evaluation = JSON.parse(cleanedResponse);

      // Validate and assign message IDs
      if (evaluation.memories && Array.isArray(evaluation.memories)) {
        evaluation.memories.forEach((memory: any, index: number) => {
          if (!memory.sourceMessageId && messages[index]) {
            memory.sourceMessageId = messages[index].id;
          }
        });
      }

      return evaluation;

    } catch (error) {
      console.error('Error in contextual message evaluation:', error);
      return {
        memories: [],
        contextSummary: 'Evaluation failed'
      };
    }
  }

  /**
   * Checks if a memory is a duplicate of existing memories
   */
  private checkForDuplicateMemory(newMemory: string, existingMemories: UserMemory[]): boolean {
    const newMemoryLower = newMemory.toLowerCase().trim();

    return existingMemories.some(existing => {
      const existingLower = existing.memory.toLowerCase().trim();

      // Exact match
      if (existingLower === newMemoryLower) {
        return true;
      }

      // High similarity (80% overlap in words)
      const newWords = new Set(newMemoryLower.split(' ').filter(w => w.length > 2));
      const existingWords = new Set(existingLower.split(' ').filter(w => w.length > 2));

      const intersection = new Set([...newWords].filter(x => existingWords.has(x)));
      const union = new Set([...newWords, ...existingWords]);

      const similarity = intersection.size / union.size;
      return similarity > 0.8;
    });
  }

  /**
   * Builds the evaluation prompt for the AI
   */
  private buildEvaluationPrompt(messageContent: string, username: string): string {
    return `Evaluate this message from user "${username}" for memory-worthy information:

Message: "${messageContent}"

Determine if this message contains information worth remembering about ${username} for future personalized conversations.`;
  }

  /**
   * Gets memory statistics for a user
   */
  async getMemoryStats(userId: string): Promise<{
    totalMemories: number;
    categoryCounts: Record<string, number>;
    oldestMemory?: string;
    newestMemory?: string;
  }> {
    try {
      const memories = await this.storage.getUserMemories(userId);
      
      if (!memories || memories.length === 0) {
        return {
          totalMemories: 0,
          categoryCounts: {}
        };
      }

      const categoryCounts: Record<string, number> = {};
      memories.forEach(memory => {
        const category = memory.category || 'uncategorized';
        categoryCounts[category] = (categoryCounts[category] || 0) + 1;
      });

      const sortedByDate = memories.sort((a, b) => 
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );

      return {
        totalMemories: memories.length,
        categoryCounts,
        oldestMemory: sortedByDate[0]?.timestamp,
        newestMemory: sortedByDate[sortedByDate.length - 1]?.timestamp
      };

    } catch (error) {
      console.error('Error getting memory stats:', error);
      return {
        totalMemories: 0,
        categoryCounts: {}
      };
    }
  }

  /**
   * Manually add a memory (for testing or admin purposes)
   */
  async addManualMemory(userId: string, memoryText: string, category?: string): Promise<boolean> {
    const memory: UserMemory = {
      memory: memoryText,
      timestamp: new Date().toISOString(),
      confidence: 1.0, // Manual memories have full confidence
      category: category || 'manual'
    };

    return await this.storage.addMemory(userId, memory);
  }

  /**
   * Remove a specific memory
   */
  async removeMemory(userId: string, memoryIndex: number): Promise<boolean> {
    return await this.storage.removeMemory(userId, memoryIndex);
  }

  /**
   * Clear all memories for a user
   */
  async clearUserMemories(userId: string): Promise<boolean> {
    return await this.storage.clearUserMemories(userId);
  }
}
