import * as fs from 'fs/promises';
import * as path from 'path';
import { UserMemory, UserMemoryStorage } from '../types/message.js';

export class MemoryStorage {
  private readonly storageDir: string;
  private readonly backupDir: string;
  private readonly maxBackups: number = 5;

  constructor(storageDir: string = 'data/memories') {
    this.storageDir = storageDir;
    this.backupDir = path.join(storageDir, 'backups');
    this.ensureDirectoriesExist();
  }

  /**
   * Ensures storage directories exist
   */
  private async ensureDirectoriesExist(): Promise<void> {
    try {
      await fs.mkdir(this.storageDir, { recursive: true });
      await fs.mkdir(this.backupDir, { recursive: true });
    } catch (error) {
      console.error('Error creating storage directories:', error);
    }
  }

  /**
   * Gets the file path for a user's memory storage
   */
  private getUserMemoryPath(userId: string): string {
    return path.join(this.storageDir, `${userId}.json`);
  }

  /**
   * Gets the backup file path for a user's memory storage
   */
  private getUserBackupPath(userId: string, timestamp: string): string {
    return path.join(this.backupDir, `${userId}_${timestamp}.json`);
  }

  /**
   * Loads user memories from storage
   */
  async getUserMemories(userId: string): Promise<UserMemory[]> {
    try {
      const filePath = this.getUserMemoryPath(userId);
      
      // Check if file exists
      try {
        await fs.access(filePath);
      } catch {
        // File doesn't exist, return empty array
        return [];
      }

      const data = await fs.readFile(filePath, 'utf-8');
      const storage: UserMemoryStorage = JSON.parse(data);
      
      // Validate the storage structure
      if (!storage.memories || !Array.isArray(storage.memories)) {
        console.warn(`Invalid memory storage format for user ${userId}`);
        return [];
      }

      return storage.memories;

    } catch (error) {
      console.error(`Error loading memories for user ${userId}:`, error);
      return [];
    }
  }

  /**
   * Saves user memories to storage
   */
  private async saveUserMemories(userId: string, memories: UserMemory[]): Promise<boolean> {
    try {
      const storage: UserMemoryStorage = {
        userId,
        memories,
        lastUpdated: new Date().toISOString(),
        totalMemories: memories.length
      };

      const filePath = this.getUserMemoryPath(userId);
      
      // Create backup before saving
      await this.createBackup(userId);
      
      // Save the updated memories
      await fs.writeFile(filePath, JSON.stringify(storage, null, 2), 'utf-8');
      
      console.log(`💾 Saved ${memories.length} memories for user ${userId}`);
      return true;

    } catch (error) {
      console.error(`Error saving memories for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Adds a new memory for a user
   */
  async addMemory(userId: string, memory: UserMemory): Promise<boolean> {
    try {
      const existingMemories = await this.getUserMemories(userId);
      
      // Check for duplicate memories (same content within last 24 hours)
      const isDuplicate = existingMemories.some(existing => {
        const timeDiff = new Date().getTime() - new Date(existing.timestamp).getTime();
        const hoursDiff = timeDiff / (1000 * 60 * 60);
        
        return existing.memory.toLowerCase() === memory.memory.toLowerCase() && 
               hoursDiff < 24;
      });

      if (isDuplicate) {
        console.log(`🔄 Duplicate memory detected for user ${userId}, skipping`);
        return false;
      }

      // Add the new memory
      existingMemories.push(memory);
      
      // Keep only the most recent memories (limit to prevent storage bloat)
      const maxMemories = 1000; // Configurable limit
      if (existingMemories.length > maxMemories) {
        existingMemories.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
        existingMemories.splice(maxMemories);
      }

      return await this.saveUserMemories(userId, existingMemories);

    } catch (error) {
      console.error(`Error adding memory for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Removes a memory by index
   */
  async removeMemory(userId: string, memoryIndex: number): Promise<boolean> {
    try {
      const memories = await this.getUserMemories(userId);
      
      if (memoryIndex < 0 || memoryIndex >= memories.length) {
        console.warn(`Invalid memory index ${memoryIndex} for user ${userId}`);
        return false;
      }

      memories.splice(memoryIndex, 1);
      return await this.saveUserMemories(userId, memories);

    } catch (error) {
      console.error(`Error removing memory for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Clears all memories for a user
   */
  async clearUserMemories(userId: string): Promise<boolean> {
    try {
      // Create backup before clearing
      await this.createBackup(userId);
      
      return await this.saveUserMemories(userId, []);

    } catch (error) {
      console.error(`Error clearing memories for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Creates a backup of user memories
   */
  private async createBackup(userId: string): Promise<void> {
    try {
      const filePath = this.getUserMemoryPath(userId);
      
      // Check if the original file exists
      try {
        await fs.access(filePath);
      } catch {
        // No file to backup
        return;
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = this.getUserBackupPath(userId, timestamp);
      
      // Copy the file to backup location
      await fs.copyFile(filePath, backupPath);
      
      // Clean up old backups
      await this.cleanupOldBackups(userId);

    } catch (error) {
      console.error(`Error creating backup for user ${userId}:`, error);
    }
  }

  /**
   * Cleans up old backup files, keeping only the most recent ones
   */
  private async cleanupOldBackups(userId: string): Promise<void> {
    try {
      const files = await fs.readdir(this.backupDir);
      const userBackups = files
        .filter(file => file.startsWith(`${userId}_`) && file.endsWith('.json'))
        .map(file => ({
          name: file,
          path: path.join(this.backupDir, file),
          timestamp: file.replace(`${userId}_`, '').replace('.json', '')
        }))
        .sort((a, b) => b.timestamp.localeCompare(a.timestamp));

      // Remove old backups beyond the limit
      if (userBackups.length > this.maxBackups) {
        const toDelete = userBackups.slice(this.maxBackups);
        for (const backup of toDelete) {
          await fs.unlink(backup.path);
        }
      }

    } catch (error) {
      console.error(`Error cleaning up backups for user ${userId}:`, error);
    }
  }

  /**
   * Gets a list of all users with stored memories
   */
  async getAllUsers(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.storageDir);
      return files
        .filter(file => file.endsWith('.json') && !file.includes('_'))
        .map(file => file.replace('.json', ''));

    } catch (error) {
      console.error('Error getting user list:', error);
      return [];
    }
  }

  /**
   * Gets storage statistics
   */
  async getStorageStats(): Promise<{
    totalUsers: number;
    totalMemories: number;
    storageSize: number;
    oldestMemory?: string;
    newestMemory?: string;
  }> {
    try {
      const users = await this.getAllUsers();
      let totalMemories = 0;
      let oldestTimestamp: string | undefined;
      let newestTimestamp: string | undefined;

      for (const userId of users) {
        const memories = await this.getUserMemories(userId);
        totalMemories += memories.length;

        for (const memory of memories) {
          if (!oldestTimestamp || memory.timestamp < oldestTimestamp) {
            oldestTimestamp = memory.timestamp;
          }
          if (!newestTimestamp || memory.timestamp > newestTimestamp) {
            newestTimestamp = memory.timestamp;
          }
        }
      }

      // Calculate storage directory size
      let storageSize = 0;
      try {
        const files = await fs.readdir(this.storageDir);
        for (const file of files) {
          const filePath = path.join(this.storageDir, file);
          const stats = await fs.stat(filePath);
          if (stats.isFile()) {
            storageSize += stats.size;
          }
        }
      } catch (error) {
        console.error('Error calculating storage size:', error);
      }

      return {
        totalUsers: users.length,
        totalMemories,
        storageSize,
        oldestMemory: oldestTimestamp,
        newestMemory: newestTimestamp
      };

    } catch (error) {
      console.error('Error getting storage stats:', error);
      return {
        totalUsers: 0,
        totalMemories: 0,
        storageSize: 0
      };
    }
  }

  /**
   * Exports all memories for a user as a readable format
   */
  async exportUserMemories(userId: string): Promise<string | null> {
    try {
      const memories = await this.getUserMemories(userId);
      
      if (memories.length === 0) {
        return null;
      }

      let export_text = `Memory Export for User: ${userId}\n`;
      export_text += `Generated: ${new Date().toISOString()}\n`;
      export_text += `Total Memories: ${memories.length}\n\n`;

      memories
        .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
        .forEach((memory, index) => {
          export_text += `${index + 1}. [${memory.timestamp}] `;
          if (memory.category) {
            export_text += `(${memory.category}) `;
          }
          export_text += `${memory.memory}`;
          if (memory.confidence) {
            export_text += ` [Confidence: ${(memory.confidence * 100).toFixed(0)}%]`;
          }
          export_text += '\n';
        });

      return export_text;

    } catch (error) {
      console.error(`Error exporting memories for user ${userId}:`, error);
      return null;
    }
  }
}
