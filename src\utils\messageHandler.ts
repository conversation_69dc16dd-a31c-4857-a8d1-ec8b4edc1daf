import { DiscordMessage } from '../types/message.js';
import { botConfig } from '../config/personality.js';
import { MessageContextManager } from './messageContext.js';
import { ConversationStorage } from './conversationStorage.js';
import { AIService } from './aiService.js';
import { MemoryService } from './memoryService.js';

export class MessageHandler {
  private contextManager: MessageContextManager;
  private storage: ConversationStorage;
  private aiService: AIService;
  private memoryService: MemoryService;
  private processingMessages: Set<string> = new Set();

  constructor() {
    this.contextManager = new MessageContextManager();
    this.storage = new ConversationStorage();
    this.aiService = new AIService();
    this.memoryService = new MemoryService();
  }

  async handleMessage(client: any, message: DiscordMessage): Promise<void> {
    try {
      // Prevent duplicate processing
      if (this.processingMessages.has(message.id)) {
        return;
      }
      this.processingMessages.add(message.id);

      console.log(`Processing message from ${message.author.username || message.author.id}: ${message.content}`);

      // Check if we should respond to this message
      if (!this.shouldRespondToMessage(message)) {
        // Still store the message for context even if we don't respond
        this.contextManager.addMessageToContext(message.channel_id, message);
        return;
      }

      // Show typing indicator
      console.log('Showing typing indicator...');
      client.send_single_type_notification(message.channel_id);

      // Fetch and update conversation context
      console.log('Fetching conversation context...');
      const context = await this.contextManager.fetchAndStoreMessages(
        client, 
        message.channel_id, 
        message
      );

      // Save conversation to storage
      await this.storage.saveConversation(context);

      // Process messages for memory with context (async, don't wait for it)
      this.processMessagesForMemoryWithContext(context.messages, message.author.id).catch((error: any) => {
        console.error('Error processing messages for memory with context:', error);
      });

      // Convert to chat messages for AI
      const chatMessages = this.contextManager.convertToChatMessages(context);

      // Get memory context for the user
      const memoryContext = await this.memoryService.getMemoryContext(
        message.author.id,
        message.content
      );

      // Generate AI response
      console.log('Generating AI response...');
      const response = await this.aiService.generateContextualResponse(
        chatMessages,
        message.content,
        {
          isDirectMessage: this.isDirectMessage(message),
          channelName: await this.getChannelName(client, message.channel_id),
        },
        memoryContext
      );

      // Stop typing indicator
      // console.log('Stopping typing indicator...');
      // client.stop_type();

      // Add a small delay to make the typing feel more natural
      // await this.delay(botConfig.typingDelay);

      // Add a small delay between messages to simulate typing
      await this.delay(botConfig.typingDelay);

      // If the response has line breaks, split it into multiple messages
      if (response.includes('\n')) {
        const messages = response.split('\n').map(line => line.trim()).filter(line => line);
        for (const line of messages) {
          console.log('Sending response line:', line);
          await client.send(message.channel_id, {
            content: line
          });
          // If this is not the last line, show typing indicator again
          if (line !== messages[messages.length - 1]) {
            // Show typing indicator
            console.log('Showing typing indicator...');
            client.send_single_type_notification(message.channel_id);

            // Wait more depending on the length of the next line
            const nextLineLength = line.length;
            const nextDelay = Math.min(nextLineLength * 50, botConfig.maxlineDelay);
            
            console.log(`Waiting for ${nextDelay}ms before sending next line...`);
            await this.delay(nextDelay);
          }
        }
        return; // Exit after sending all lines
      } else {
        // Send response as a single message
        console.log('Sending response:', response);
        await client.send(message.channel_id, {
          content: response
        });
      }

      console.log('Response sent successfully');

    } catch (error) {
      console.error('Error handling message:', error);
      
      // Try to send an error message if possible
      try {
        await client.send(message.channel_id, {
          content: "Sorry, I encountered an error while processing your message... 😅"
        });
      } catch (sendError) {
        console.error('Failed to send error message:', sendError);
      }
    } finally {
      // Clean up processing flag
      this.processingMessages.delete(message.id);
    }
  }

  private shouldRespondToMessage(message: DiscordMessage): boolean {
    // Don't respond to own messages
    if (botConfig.ignoreOwnMessages && this.isBotMessage(message)) {
      return false;
    }

    // Check if message is from target user
    // if (botConfig.targetUserId && message.author.id === botConfig.targetUserId) {
    //   return true;
    // }

    // Check for mentions (if enabled)
    if (botConfig.respondToMentions && this.isMentioned(message)) {
      return true;
    }

    // Check if it's a DM (if enabled)
    if (botConfig.respondToDirectMessages && this.isDirectMessage(message)) {
      return true;
    }

    return false;
  }

  private isBotMessage(message: DiscordMessage): boolean {
    // This is a simple check - you might want to store the bot's user ID
    // and check against that instead
    const botIndicators = ['monika', 'bot'];
    const username = (message.author.username || '').toLowerCase();
    const globalName = (message.author.global_name || '').toLowerCase();
    
    return botIndicators.some(indicator => 
      username.includes(indicator) || globalName.includes(indicator)
    );
  }

  private isMentioned(message: DiscordMessage): boolean {
    // Check if the bot is mentioned in the message
    // This is a basic implementation - Discord bots usually have their user ID in mentions
    return !!(message.mentions && message.mentions.length > 0);
  }

  private isDirectMessage(message: DiscordMessage): boolean {
    // In Discord, DM channels typically don't belong to a guild
    // This is a basic check - you might need to adjust based on your Discord library
    return !message.channel_id.startsWith('guild_') && 
           message.channel_id.length < 20; // DM channels have shorter IDs typically
  }

  private async getChannelName(client: any, channelId: string): Promise<string | undefined> {
    try {
      // This would depend on your Discord library's API
      // For now, return undefined as we don't have channel info access
      return undefined;
    } catch (error) {
      console.error('Error getting channel name:', error);
      return undefined;
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Method to get conversation statistics
  getConversationStats(channelId: string): {
    messageCount: number;
    lastActivity: string | null;
    participants: number;
  } | null {
    const context = this.contextManager.getContext(channelId);
    if (!context) {
      return null;
    }

    const participants = new Set(context.messages.map(msg => msg.author.id));
    const lastMessage = context.messages[context.messages.length - 1];

    return {
      messageCount: context.messageCount,
      lastActivity: lastMessage ? lastMessage.timestamp : null,
      participants: participants.size
    };
  }

  // Method to export conversation
  async exportConversation(channelId: string): Promise<string | null> {
    return this.storage.exportConversationAsText(channelId);
  }

  // Method to clear conversation history
  async clearConversation(channelId: string): Promise<boolean> {
    return this.storage.deleteConversation(channelId);
  }

  // Method to get all conversations
  getAllConversations(): string[] {
    return this.storage.getConversationList();
  }

  // Memory-related methods
  private async processMessagesForMemoryWithContext(messages: DiscordMessage[], userId: string): Promise<void> {
    try {
      // Only process messages from the target user for memory
      // if (userId !== botConfig.targetUserId) {
      //   return;
      // }

      // Filter out very short messages or commands
      const validMessages = messages.filter(msg =>
        msg.author.id === userId &&
        msg.content.length >= 10 &&
        !msg.content.startsWith('!')
      );

      if (validMessages.length === 0) {
        return;
      }

      console.log(`🧠 Evaluating ${validMessages.length} messages with context for memory processing...`);
      const result = await this.memoryService.processMessagesWithContext(validMessages, userId);

      if (result.processedCount > 0) {
        console.log(`💾 Processed ${result.processedCount} new memories, skipped ${result.skippedDuplicates} duplicates`);
      }

    } catch (error) {
      console.error('Error in processMessagesForMemoryWithContext:', error);
    }
  }

  private async processMessageForMemory(message: DiscordMessage): Promise<void> {
    try {
      // Only process messages from the target user for memory
      // if (message.author.id !== botConfig.targetUserId) {
      //   return;
      // }

      // Skip very short messages or commands
      if (message.content.length < 10 || message.content.startsWith('!')) {
        return;
      }

      console.log(`🧠 Evaluating single message for memory: ${message.content.substring(0, 50)}...`);
      await this.memoryService.processMessageForMemory(message);

    } catch (error) {
      console.error('Error in processMessageForMemory:', error);
    }
  }

  // Method to get memory context for a user
  async getMemoryContext(userId: string, currentMessage?: string) {
    return await this.memoryService.getMemoryContext(userId, currentMessage);
  }

  // Method to get memory statistics
  async getMemoryStats(userId: string) {
    return await this.memoryService.getMemoryStats(userId);
  }

  // Method to manually add a memory
  async addManualMemory(userId: string, memoryText: string, category?: string): Promise<boolean> {
    return await this.memoryService.addManualMemory(userId, memoryText, category);
  }

  // Method to clear user memories
  async clearUserMemories(userId: string): Promise<boolean> {
    return await this.memoryService.clearUserMemories(userId);
  }

  // Method to process messages with context for memory (public interface)
  async processMessagesWithContext(messages: DiscordMessage[], userId: string) {
    return await this.memoryService.processMessagesWithContext(messages, userId);
  }

  // Method to process a single message for memory (fallback)
  async processSingleMessageForMemory(message: DiscordMessage): Promise<boolean> {
    // if (message.author.id !== botConfig.targetUserId) {
    //   return false;
    // }

    if (message.content.length < 10 || message.content.startsWith('!')) {
      return false;
    }

    return await this.memoryService.processMessageForMemory(message);
  }
}
