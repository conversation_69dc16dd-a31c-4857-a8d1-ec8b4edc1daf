import { StatusConfig } from '../config/personality.js';

export type DiscordStatus = "online" | "idle" | "dnd" | "invisible";

export interface StatusManagerEvents {
  statusChanged: (oldStatus: DiscordStatus, newStatus: DiscordStatus) => void;
  idleTimerStarted: (timeoutMinutes: number) => void;
  idleTimerStopped: () => void;
}

export class StatusManager {
  private client: any;
  private config: StatusConfig;
  private currentStatus: DiscordStatus;
  private idleTimer: NodeJS.Timeout | null = null;
  private lastActivity: Date;
  private eventHandlers: Partial<StatusManagerEvents> = {};

  constructor(client: any, config: StatusConfig) {
    this.client = client;
    this.config = config;
    this.currentStatus = config.autoManagement.defaultStatus;
    this.lastActivity = new Date();
  }

  /**
   * Initialize the status manager and set initial status
   */
  async initialize(): Promise<void> {
    if (!this.config.enabled) {
      console.log('📴 Status management is disabled');
      return;
    }

    console.log('🔄 Initializing status manager...');
    
    // Set initial status
    await this.setStatus(this.config.autoManagement.defaultStatus);
    
    // Start idle timer if auto-management is enabled
    if (this.config.autoManagement.goIdleAfterInactivity) {
      this.startIdleTimer();
    }

    console.log(`✅ Status manager initialized with status: ${this.currentStatus}`);
  }

  /**
   * Set Discord status
   */
  async setStatus(status: DiscordStatus): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    try {
      const oldStatus = this.currentStatus;
      
      // Use the configured status mapping
      const statusToSet = this.config.statuses[status] as DiscordStatus;
      
      await this.client.change_status(statusToSet);
      this.currentStatus = status;
      
      console.log(`🔄 Status changed: ${oldStatus} → ${status}`);
      
      // Emit status changed event
      if (this.eventHandlers.statusChanged && oldStatus !== status) {
        this.eventHandlers.statusChanged(oldStatus, status);
      }
      
    } catch (error) {
      console.error('❌ Failed to change status:', error);
      throw error;
    }
  }

  /**
   * Handle message activity - resets idle timer and sets online if configured
   */
  async onMessageActivity(): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    this.lastActivity = new Date();
    
    // Reset idle timer
    this.resetIdleTimer();
    
    // Go online if configured and not already online
    if (this.config.autoManagement.goOnlineOnMessage && this.currentStatus !== 'online') {
      await this.setStatus('online');
    }
  }

  /**
   * Start the idle timer
   */
  private startIdleTimer(): void {
    if (!this.config.autoManagement.goIdleAfterInactivity) {
      return;
    }

    this.clearIdleTimer();
    
    const timeoutMs = this.config.idleTimeout * 60 * 1000; // Convert minutes to milliseconds
    
    this.idleTimer = setTimeout(async () => {
      if (this.currentStatus === 'online') {
        console.log(`⏰ Going idle after ${this.config.idleTimeout} minutes of inactivity`);
        await this.setStatus('idle');
      }
    }, timeoutMs);

    console.log(`⏱️ Idle timer started (${this.config.idleTimeout} minutes)`);
    
    // Emit idle timer started event
    if (this.eventHandlers.idleTimerStarted) {
      this.eventHandlers.idleTimerStarted(this.config.idleTimeout);
    }
  }

  /**
   * Reset the idle timer (restart it)
   */
  private resetIdleTimer(): void {
    if (this.config.autoManagement.goIdleAfterInactivity) {
      this.startIdleTimer();
    }
  }

  /**
   * Clear the idle timer
   */
  private clearIdleTimer(): void {
    if (this.idleTimer) {
      clearTimeout(this.idleTimer);
      this.idleTimer = null;
      
      // Emit idle timer stopped event
      if (this.eventHandlers.idleTimerStopped) {
        this.eventHandlers.idleTimerStopped();
      }
    }
  }

  /**
   * Stop the status manager and clean up timers
   */
  stop(): void {
    console.log('🛑 Stopping status manager...');
    this.clearIdleTimer();
    this.eventHandlers = {};
  }

  /**
   * Get current status
   */
  getCurrentStatus(): DiscordStatus {
    return this.currentStatus;
  }

  /**
   * Get last activity time
   */
  getLastActivity(): Date {
    return this.lastActivity;
  }

  /**
   * Get time until idle (in minutes)
   */
  getTimeUntilIdle(): number | null {
    if (!this.config.autoManagement.goIdleAfterInactivity || !this.idleTimer) {
      return null;
    }

    const timeSinceActivity = Date.now() - this.lastActivity.getTime();
    const timeUntilIdle = (this.config.idleTimeout * 60 * 1000) - timeSinceActivity;
    
    return Math.max(0, Math.ceil(timeUntilIdle / (60 * 1000))); // Return minutes
  }

  /**
   * Update configuration at runtime
   */
  updateConfig(newConfig: Partial<StatusConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Status manager configuration updated');
    
    // Restart idle timer if timeout changed
    if (newConfig.idleTimeout !== undefined && this.config.autoManagement.goIdleAfterInactivity) {
      this.resetIdleTimer();
    }
  }

  /**
   * Register event handlers
   */
  on<K extends keyof StatusManagerEvents>(event: K, handler: StatusManagerEvents[K]): void {
    this.eventHandlers[event] = handler;
  }

  /**
   * Remove event handlers
   */
  off<K extends keyof StatusManagerEvents>(event: K): void {
    delete this.eventHandlers[event];
  }

  /**
   * Force set status (bypasses auto-management temporarily)
   */
  async forceSetStatus(status: DiscordStatus): Promise<void> {
    console.log(`🔧 Force setting status to: ${status}`);
    await this.setStatus(status);
    
    // Reset idle timer to resume auto-management
    if (this.config.autoManagement.goIdleAfterInactivity) {
      this.resetIdleTimer();
    }
  }

  /**
   * Get status manager statistics
   */
  getStats(): {
    enabled: boolean;
    currentStatus: DiscordStatus;
    lastActivity: string;
    timeUntilIdle: number | null;
    idleTimeout: number;
    autoManagement: StatusConfig['autoManagement'];
  } {
    return {
      enabled: this.config.enabled,
      currentStatus: this.currentStatus,
      lastActivity: this.lastActivity.toISOString(),
      timeUntilIdle: this.getTimeUntilIdle(),
      idleTimeout: this.config.idleTimeout,
      autoManagement: this.config.autoManagement
    };
  }
}
